// Core or Framework Imports
import { useState, useEffect, useCallback } from "react";
import { AppService } from "./services/AppService";
import { ConversationStorage } from "./services/ConversationStorage";
import { MovistarOttService } from "./services/MovistarOttService";
import { MovistarDirectService } from "./services/MovistarDirectService";
import { SimpleVoiceChat } from "./components/SimpleVoiceChat";
import { GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import "./App.css";

function App() {
  // Game state management
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [, setCharacterError] = useState<string>("");

  // Movistar service state
  const [movistarLoading, setMovistarLoading] = useState<boolean>(false);
  const [movistarResult, setMovistarResult] = useState<any>(null);
  const [movistarError, setMovistarError] = useState<string>("");

  // Movistar Direct service state
  const [movistarDirectLoading, setMovistarDirectLoading] = useState<boolean>(false);
  const [movistarDirectResult, setMovistarDirectResult] = useState<string>("");
  const [movistarDirectError, setMovistarDirectError] = useState<string>("");

  // Service instances (singletons)
  const appService = AppService.getInstance();
  const conversationStorage = ConversationStorage.getInstance();

  /**
   * Initialize app by clearing previous conversations
   * This ensures a fresh start for each session
   */
  useEffect(() => {
    conversationStorage.clearAllConversations();
    // console.log("🧹 Conversaciones limpiadas al inicializar la aplicación");
  }, [conversationStorage]);

  /**
   * Configure audio playback callback
   * Sets up automatic audio playback with fallback handling
   */
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      // Small delay to ensure proper audio context initialization
      setTimeout(() => {
        const audioFinishedCallback = appService.getAudioFinishedCallback();
        playAudioWithFallback(audioUrl, audioFinishedCallback);
      }, 100);
    });
  }, [appService]);

  /**
   * Debug preset configuration on app initialization
   */
  useEffect(() => {
    appService.debugPresetConfiguration();
  }, []);

  /**
   * Extract response text from API response object
   * Handles multiple possible response field names
   */
  const extractResponseText = useCallback((response: any, fallback = "Respuesta no encontrada") => {
    return response.response ||
           response.output ||
           response.result ||
           response.text ||
           response.content ||
           fallback;
  }, []);

  /**
   * Main game initialization function
   * Combines character generation and game start in a single flow
   */
  const handleStartGameDirectly = useCallback(async () => {
    setAiLoading(true);
    setCharacterError("");

    try {
      // Step 1: Generate character
      const characterResponse = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterText = extractResponseText(characterResponse);
      if (!characterText || characterText === "Respuesta no encontrada") {
        throw new Error("No se pudo generar el personaje");
      }

      setGeneratedCharacter(characterText);

      // Step 2: Start game immediately
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        characterText
      );

      const responseText = extractResponseText(gameResponse);
      setInitialMessage(responseText);

    } catch (error) {
      console.error("❌ Error en inicio directo del juego:", error);
      setCharacterError("Error al generar personaje o iniciar juego. Inténtalo de nuevo.");
      setGameStarted(false);
      setGeneratedCharacter("");
    } finally {
      setAiLoading(false);
    }
  }, [appService, extractResponseText]);

  /**
   * Reset game to initial state
   * Clears all game data and conversation history
   */
  const handleResetGame = useCallback(() => {
    setGeneratedCharacter("");
    setGameStarted(false);
    setInitialMessage("");
    setCharacterError("");
    conversationStorage.clearAllConversations();
    console.log("🔄 Juego reiniciado y conversaciones limpiadas");
  }, [conversationStorage]);

  /**
   * Test Movistar OTT Service
   * Initializes the service and performs a test search
   */
  const handleTestMovistarService = useCallback(async () => {
    setMovistarLoading(true);
    setMovistarError("");
    setMovistarResult(null);

    try {
      console.log("🎬 Iniciando prueba del servicio Movistar+...");

      const movistarService = new MovistarOttService();
      await movistarService.init();

      // Realizar una búsqueda de prueba
      const testResult = await movistarService.searchByActor("Tom Hanks");

      setMovistarResult(testResult);
      console.log("✅ Prueba del servicio Movistar+ exitosa:", testResult);

      // Cerrar la conexión
      await movistarService.close();

    } catch (error) {
      console.error("❌ Error probando servicio Movistar+:", error);
      setMovistarError(error instanceof Error ? error.message : "Error desconocido");
    } finally {
      setMovistarLoading(false);
    }
  }, []);

  /**
   * Test Movistar Direct Service
   * Tests the new direct API service for Movistar+
   */
  const handleTestMovistarDirectService = useCallback(async () => {
    setMovistarDirectLoading(true);
    setMovistarDirectError("");
    setMovistarDirectResult("");

    try {
      console.log("🎬 Iniciando prueba del servicio Movistar+ Directo...");

      const movistarDirectService = new MovistarDirectService();

      // Verificar configuración
      if (!movistarDirectService.isConfigured()) {
        throw new Error("El servicio no está configurado correctamente. Verifica la variable VITE_MOVISTAR_API_URL.");
      }

      // Realizar una búsqueda de prueba por actor
      const testResult = await movistarDirectService.searchByActor("Tom Hanks");

      setMovistarDirectResult(testResult);
      console.log("✅ Prueba del servicio Movistar+ Directo exitosa:", testResult);

    } catch (error) {
      console.error("❌ Error probando servicio Movistar+ Directo:", error);
      setMovistarDirectError(error instanceof Error ? error.message : "Error desconocido");
    } finally {
      setMovistarDirectLoading(false);
    }
  }, []);

  return (
    <>
      <div className="card">
        <div className="game-container">
          <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

          {/* Quick Start Section */}
          {!gameStarted && (
            <div className="quick-start-section">
              <h4 className="quick-start-title">🚀 Inicio Rápido</h4>
              <p className="quick-start-description">
                ¡Comienza a jugar inmediatamente! Se generará un personaje
                automáticamente y comenzará el juego.
              </p>
              <div className="buttons-container">
                <button
                  onClick={handleStartGameDirectly}
                  disabled={aiLoading}
                  className="primary-button"
                >
                  {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
                </button>

                {/* <button
                  onClick={handleTestMovistarService}
                  disabled={movistarLoading}
                  className="secondary-button"
                >
                  {movistarLoading ? "Probando Servicio..." : "🎬 PROBAR SERVICIO"}
                </button> */}

                <button
                  onClick={handleTestMovistarDirectService}
                  disabled={movistarDirectLoading}
                  className="secondary-button"
                >
                  {movistarDirectLoading ? "Probando Servicio Directo..." : "🎬 PROBAR SERVICIO DIRECTO"}
                </button>
              </div>
            </div>
          )}

          {/* Movistar Service Results Section */}
          {(movistarResult || movistarError) && (
            <div className="movistar-results-section">
              <h4 className="movistar-results-title">📺 Resultados del Servicio Movistar+</h4>

              {movistarError && (
                <div className="error-message">
                  ❌ Error: {movistarError}
                </div>
              )}

              {movistarResult && (
                <div className="movistar-success">
                  <p className="success-message">✅ Servicio probado exitosamente</p>
                  <div className="result-data">
                    <pre>{JSON.stringify(movistarResult, null, 2)}</pre>
                  </div>
                </div>
              )}

              <button
                onClick={() => {
                  setMovistarResult(null);
                  setMovistarError("");
                }}
                className="clear-results-button"
              >
                🗑️ Limpiar Resultados
              </button>
            </div>
          )}

          {/* Movistar Direct Service Results Section */}
          {(movistarDirectResult || movistarDirectError) && (
            <div className="movistar-results-section">
              <h4 className="movistar-results-title">🎬 Resultados del Servicio Movistar+ Directo</h4>

              {movistarDirectError && (
                <div className="error-message">
                  ❌ Error: {movistarDirectError}
                </div>
              )}

              {movistarDirectResult && (
                <div className="movistar-success">
                  <p className="success-message">✅ Servicio directo probado exitosamente</p>
                  <div className="result-data">
                    <pre style={{ whiteSpace: 'pre-wrap', textAlign: 'left' }}>{movistarDirectResult}</pre>
                  </div>
                </div>
              )}

              <button
                onClick={() => {
                  setMovistarDirectResult("");
                  setMovistarDirectError("");
                }}
                className="clear-results-button"
              >
                🗑️ Limpiar Resultados
              </button>
            </div>
          )}

          {/* Voice Chat Component - handles all conversation history */}
          <SimpleVoiceChat
            generatedCharacter={generatedCharacter}
            isGameStarted={gameStarted}
            initialMessage={initialMessage}
          />

          {/* Reset Game Section */}
          {(generatedCharacter || gameStarted) && (
            <div className="reset-section">
              <button onClick={handleResetGame} className="reset-button">
                🔄 Reiniciar Juego
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default App;
