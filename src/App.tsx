// Core or Framework Imports
import { useState, useEffect, useCallback } from "react";
import { AppService } from "./services/AppService";
import { ConversationStorage } from "./services/ConversationStorage";
import { MovistarOttService } from "./services/MovistarOttService";
import { SimpleVoiceChat } from "./components/SimpleVoiceChat";
import { GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import "./App.css";

function App() {
  // Game state management
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [, setCharacterError] = useState<string>("");

  // Movistar service state
  const [movistarLoading, setMovistarLoading] = useState<boolean>(false);
  const [movistarResult, setMovistarResult] = useState<any>(null);
  const [movistarError, setMovistarError] = useState<string>("");

  // Service instances (singletons)
  const appService = AppService.getInstance();
  const conversationStorage = ConversationStorage.getInstance();

  /**
   * Initialize app by clearing previous conversations
   * This ensures a fresh start for each session
   */
  useEffect(() => {
    conversationStorage.clearAllConversations();
    // console.log("🧹 Conversaciones limpiadas al inicializar la aplicación");
  }, [conversationStorage]);

  /**
   * Configure audio playback callback
   * Sets up automatic audio playback with fallback handling
   */
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      // Small delay to ensure proper audio context initialization
      setTimeout(() => {
        const audioFinishedCallback = appService.getAudioFinishedCallback();
        playAudioWithFallback(audioUrl, audioFinishedCallback);
      }, 100);
    });
  }, [appService]);

  /**
   * Debug preset configuration on app initialization
   */
  useEffect(() => {
    appService.debugPresetConfiguration();
  }, []);

  /**
   * Extract response text from API response object
   * Handles multiple possible response field names
   */
  const extractResponseText = useCallback((response: any, fallback = "Respuesta no encontrada") => {
    return response.response ||
           response.output ||
           response.result ||
           response.text ||
           response.content ||
           fallback;
  }, []);

  /**
   * Main game initialization function
   * Combines character generation and game start in a single flow
   */
  const handleStartGameDirectly = useCallback(async () => {
    setAiLoading(true);
    setCharacterError("");

    try {
      // Step 1: Generate character
      const characterResponse = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterText = extractResponseText(characterResponse);
      if (!characterText || characterText === "Respuesta no encontrada") {
        throw new Error("No se pudo generar el personaje");
      }

      setGeneratedCharacter(characterText);

      // Step 2: Start game immediately
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        characterText
      );

      const responseText = extractResponseText(gameResponse);
      setInitialMessage(responseText);

    } catch (error) {
      console.error("❌ Error en inicio directo del juego:", error);
      setCharacterError("Error al generar personaje o iniciar juego. Inténtalo de nuevo.");
      setGameStarted(false);
      setGeneratedCharacter("");
    } finally {
      setAiLoading(false);
    }
  }, [appService, extractResponseText]);

  /**
   * Reset game to initial state
   * Clears all game data and conversation history
   */
  const handleResetGame = useCallback(() => {
    setGeneratedCharacter("");
    setGameStarted(false);
    setInitialMessage("");
    setCharacterError("");
    conversationStorage.clearAllConversations();
    console.log("🔄 Juego reiniciado y conversaciones limpiadas");
  }, [conversationStorage]);

  /**
   * Test Movistar OTT Service
   * Initializes the service and performs a test search
   */
  const handleTestMovistarService = useCallback(async () => {
    setMovistarLoading(true);
    setMovistarError("");
    setMovistarResult(null);

    try {
      console.log("🎬 Iniciando prueba del servicio Movistar+...");

      const movistarService = new MovistarOttService();
      await movistarService.init();

      // Realizar una búsqueda de prueba
      const testResult = await movistarService.searchByActor("Tom Hanks");

      setMovistarResult(testResult);
      console.log("✅ Prueba del servicio Movistar+ exitosa:", testResult);

      // Cerrar la conexión
      await movistarService.close();

    } catch (error) {
      console.error("❌ Error probando servicio Movistar+:", error);
      setMovistarError(error instanceof Error ? error.message : "Error desconocido");
    } finally {
      setMovistarLoading(false);
    }
  }, []);

  return (
    <>
      <div className="card">
        <div className="game-container">
          <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

          {/* Quick Start Section */}
          {!gameStarted && (
            <div className="quick-start-section">
              <h4 className="quick-start-title">🚀 Inicio Rápido</h4>
              <p className="quick-start-description">
                ¡Comienza a jugar inmediatamente! Se generará un personaje
                automáticamente y comenzará el juego.
              </p>
              <div className="buttons-container">
                <button
                  onClick={handleStartGameDirectly}
                  disabled={aiLoading}
                  className="primary-button"
                >
                  {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
                </button>

                {/* <button
                  onClick={handleTestMovistarService}
                  disabled={movistarLoading}
                  className="secondary-button"
                >
                  {movistarLoading ? "Probando Servicio..." : "🎬 PROBAR SERVICIO"}
                </button> */}
              </div>
            </div>
          )}

          {/* Movistar Service Results Section */}
          {(movistarResult || movistarError) && (
            <div className="movistar-results-section">
              <h4 className="movistar-results-title">📺 Resultados del Servicio Movistar+</h4>

              {movistarError && (
                <div className="error-message">
                  ❌ Error: {movistarError}
                </div>
              )}

              {movistarResult && (
                <div className="movistar-success">
                  <p className="success-message">✅ Servicio probado exitosamente</p>
                  <div className="result-data">
                    <pre>{JSON.stringify(movistarResult, null, 2)}</pre>
                  </div>
                </div>
              )}

              <button
                onClick={() => {
                  setMovistarResult(null);
                  setMovistarError("");
                }}
                className="clear-results-button"
              >
                🗑️ Limpiar Resultados
              </button>
            </div>
          )}

          {/* Voice Chat Component - handles all conversation history */}
          <SimpleVoiceChat
            generatedCharacter={generatedCharacter}
            isGameStarted={gameStarted}
            initialMessage={initialMessage}
          />

          {/* Reset Game Section */}
          {(generatedCharacter || gameStarted) && (
            <div className="reset-section">
              <button onClick={handleResetGame} className="reset-button">
                🔄 Reiniciar Juego
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default App;
